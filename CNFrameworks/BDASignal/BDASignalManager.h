







#import <Foundation/Foundation.h>
#import <UIKit/UIApplication.h>
#import "BDASignalDefinitions.h"

@interface BDASignalManager : NSObject



+ (instancetype)sharedInstance;



+ (void)registerWithOptionalData:(NSDictionary *)config;



+ (void)didFinishLaunchingWithOptions:(NSDictionary *)launchOptions connectOptions:(UISceneConnectionOptions *)connetOptions;



+ (NSString *)anylyseDeeplinkClickidWithOpenUrl:(NSString *)openUrl;



+ (void)trackEssentialEventWithName:(NSString *)key params:(NSDictionary *)params;



+ (NSString *)getClickId;



+ (NSString *)getCacheOpenUrl;



+ (NSDictionary *)getExtraParams;



+ (NSString *)getIpv4;



+ (NSString *)getWebviewUA;



+ (void)enableIdfa:(BOOL)enable;



+ (BOOL)getIdfaStatus;



+ (void)enableDelayUpload;



+ (void)startSendingEvents;



@property (nonatomic, copy) NSString *clickid;



@property (nonatomic, strong) NSMutableDictionary *extraParam;

@end


