


#import <Foundation/Foundation.h>
#import "SDWebImageCompat.h"


typedef NS_ENUM(NSUInteger, SDImageCacheConfigExpireType) {
    

    SDImageCacheConfigExpireTypeAccessDate,
    

    SDImageCacheConfigExpireTypeModificationDate,
    

    SDImageCacheConfigExpireTypeCreationDate,
    

    SDImageCacheConfigExpireTypeChangeDate,
};



@interface SDImageCacheConfig : NSObject <NSCopying>



@property (nonatomic, class, readonly, nonnull) SDImageCacheConfig *defaultCacheConfig;



@property (assign, nonatomic) BOOL shouldDisableiCloud;



@property (assign, nonatomic) BOOL shouldCacheImagesInMemory;



@property (assign, nonatomic) BOOL shouldUseWeakMemoryCache;



@property (assign, nonatomic) BOOL shouldRemoveExpiredDataWhenEnterBackground;



@property (assign, nonatomic) BOOL shouldRemoveExpiredDataWhenTerminate;



@property (assign, nonatomic) NSDataReadingOptions diskCacheReadingOptions;



@property (assign, nonatomic) NSDataWritingOptions diskCacheWritingOptions;



@property (assign, nonatomic) NSTimeInterval maxDiskAge;



@property (assign, nonatomic) NSUInteger maxDiskSize;



@property (assign, nonatomic) NSUInteger maxMemoryCost;



@property (assign, nonatomic) NSUInteger maxMemoryCount;



@property (assign, nonatomic) SDImageCacheConfigExpireType diskCacheExpireType;



@property (strong, nonatomic, nullable) NSFileManager *fileManager;



@property (strong, nonatomic, nullable) dispatch_queue_attr_t ioQueueAttributes;



@property (assign, nonatomic, nonnull) Class memoryCacheClass;



@property (assign ,nonatomic, nonnull) Class diskCacheClass;

@end
