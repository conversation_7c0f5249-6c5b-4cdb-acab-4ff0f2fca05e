


#import "SDWebImageCompat.h"
#import "NSData+ImageContentType.h"



@interface UIImage (MultiFormat)



+ (nullable UIImage *)sd_imageWithData:(nullable NSData *)data;



+ (nullable UIImage *)sd_imageWithData:(nullable NSData *)data scale:(CGFloat)scale;



+ (nullable UIImage *)sd_imageWithData:(nullable NSData *)data scale:(CGFloat)scale firstFrameOnly:(BOOL)firstFrameOnly;




- (nullable NSData *)sd_imageData;



- (nullable NSData *)sd_imageDataAsFormat:(SDImageFormat)imageFormat NS_SWIFT_NAME(sd_imageData(as:));



- (nullable NSData *)sd_imageDataAsFormat:(SDImageFormat)imageFormat compressionQuality:(double)compressionQuality NS_SWIFT_NAME(sd_imageData(as:compressionQuality:));



- (nullable NSData *)sd_imageDataAsFormat:(SDImageFormat)imageFormat compressionQuality:(double)compressionQuality firstFrameOnly:(BOOL)firstFrameOnly NS_SWIFT_NAME(sd_imageData(as:compressionQuality:firstFrameOnly:));

@end
