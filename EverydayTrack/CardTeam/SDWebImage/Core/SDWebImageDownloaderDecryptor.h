


#import <Foundation/Foundation.h>
#import "SDWebImageCompat.h"

typedef NSData * _Nullable (^SDWebImageDownloaderDecryptorBlock)(NSData * _Nonnull data, NSURLResponse * _Nullable response);



@protocol SDWebImageDownloaderDecryptor <NSObject>





- (nullable NSData *)decryptedDataWithData:(nonnull NSData *)data response:(nullable NSURLResponse *)response;

@end



@interface SDWebImageDownloaderDecryptor : NSObject <SDWebImageDownloaderDecryptor>



- (nonnull instancetype)initWithBlock:(nonnull SDWebImageDownloaderDecryptorBlock)block;



+ (nonnull instancetype)decryptorWithBlock:(nonnull SDWebImageDownloaderDecryptorBlock)block;

- (nonnull instancetype)init NS_UNAVAILABLE;
+ (nonnull instancetype)new  NS_UNAVAILABLE;

@end


@interface SDWebImageDownloaderDecryptor (Conveniences)


@property (class, readonly, nonnull) SDWebImageDownloaderDecryptor *base64Decryptor;

@end
