

#import "ShearViewController.h"
#import "Least.h"
#import "SourcesPurchasedSoftnessNetScanningConfigure.h"
#import "TerminateTaggingMajorRawRound.h"
#import "PowerMajorConfig.h"

@interface ShearViewController ()
@property (nonatomic, strong) UITextView *textView;
@property (nonatomic, strong) SourcesPurchasedSoftnessNetScanningConfigure *hexDuplexKelvinBasqueIntro;
@property (nonatomic, strong) TerminateTaggingMajorRawRound *helloShareCyclingListPhonetic;
@property (nonatomic, strong) NSDate *selectedDate; 
@end

static TerminateTaggingMajorRawRound *viewMusicTalkSliderCarHer = nil;
static SourcesPurchasedSoftnessNetScanningConfigure *hueRestartPlaneContrastDefaultsPasswords = nil;

@implementation ShearViewController

+ (void)postcardCat {
    
    [Least allowableWayBedMasterMessagingClusters];

    hueRestartPlaneContrastDefaultsPasswords = [[SourcesPurchasedSoftnessNetScanningConfigure alloc] init];
    hueRestartPlaneContrastDefaultsPasswords.appearLevel = TurnFilmProcessNaturalAxial;

    [Least mostlyExecutionNegotiateMoreSpanish:hueRestartPlaneContrastDefaultsPasswords];

    viewMusicTalkSliderCarHer = [[TerminateTaggingMajorRawRound alloc] init];
    viewMusicTalkSliderCarHer.appearLevel = BypassFailure;
    viewMusicTalkSliderCarHer.easyArm = 7;
    viewMusicTalkSliderCarHer.neutralSegmentedTextureSmallerCan = YES;
    [Least mostlyExecutionNegotiateMoreSpanish:viewMusicTalkSliderCarHer];

    [viewMusicTalkSliderCarHer wasTallSortSun];
}

+ (TerminateTaggingMajorRawRound *)loopSelectionHalfGenericEntityEldest {
    return viewMusicTalkSliderCarHer;
}
+ (SourcesPurchasedSoftnessNetScanningConfigure *)thirdDescenderFixtureBagLayoutBest {
    return hueRestartPlaneContrastDefaultsPasswords;
}

- (SourcesPurchasedSoftnessNetScanningConfigure *)hexDuplexKelvinBasqueIntro {
    return hueRestartPlaneContrastDefaultsPasswords;
}

+ (void)mapStyleViewController:(UIViewController *)parentVC {
    ShearViewController *roman = [[ShearViewController alloc] init];
    UINavigationController *out = [[UINavigationController alloc] initWithRootViewController:roman];
    out.modalPresentationStyle = UIModalPresentationFullScreen;
    [parentVC presentViewController:out animated:YES completion:nil];
}

- (void)viewDidLoad {
    [super viewDidLoad];

    self.title = wayWidthYet.redoDigitalUpperPoloSelfHash;
    self.view.backgroundColor = [UIColor systemBackgroundColor];

    self.navigationItem.leftBarButtonItem = [[UIBarButtonItem alloc]
                                            initWithBarButtonSystemItem:UIBarButtonSystemItemCancel
                                            target:self
                                            action:@selector(brownAction)];

    self.navigationItem.rightBarButtonItems = @[
        [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemRefresh
                                                      target:self
                                                      action:@selector(callCapAction)],
        [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemAction
                                                      target:self
                                                      action:@selector(briefAction)],
        [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemBookmarks
                                                      target:self
                                                      action:@selector(listOddRedAction)]
    ];
    self.navigationController.navigationBar.layoutMargins = UIEdgeInsetsMake(0, 0, 0, -10);

    _textView = [[UITextView alloc] init];
    _textView.font = [UIFont systemFontOfSize:11];
    _textView.editable = NO;
    _textView.backgroundColor = [UIColor systemBackgroundColor];
    _textView.textColor = [UIColor labelColor];
    _textView.translatesAutoresizingMaskIntoConstraints = NO;
    _textView.showsVerticalScrollIndicator = YES;
    _textView.showsHorizontalScrollIndicator = YES;
    _textView.alwaysBounceVertical = YES;
    
    _textView.scrollEnabled = YES;
    [self.view addSubview:_textView];

    [NSLayoutConstraint activateConstraints:@[
        [_textView.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor],
        [_textView.leadingAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.leadingAnchor constant:8],
        [_textView.trailingAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.trailingAnchor constant:-8],
        [_textView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor]
    ]];

    self.helloShareCyclingListPhonetic = [ShearViewController loopSelectionHalfGenericEntityEldest];

    [self gateways];
}

- (void)gateways {
    if (!self.helloShareCyclingListPhonetic) {
        _textView.text = wayWidthYet.capsCondensedVisualHexSynthesisChange;
        return;
    }

    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        NSString *logs;
        if (self.selectedDate) {
            logs = [self.helloShareCyclingListPhonetic tapsKitDateDate:self.selectedDate];
        } else {
            logs = [self.helloShareCyclingListPhonetic boundingCup];
        }

        dispatch_async(dispatch_get_main_queue(), ^{
            if (logs.length > 0) {
                self.textView.text = logs;
                
                [self.textView scrollRangeToVisible:NSMakeRange(logs.length - 1, 1)];
            } else {
                self.textView.text = wayWidthYet.sonHintInstantPageCreatorMen;
            }

            [self updateTitle];
        });
    });
}

- (void)brownAction {
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)callCapAction {
    [self gateways];
}

- (void)updateTitle {
    if (self.selectedDate) {
        NSDateFormatter *barTelugu = [[NSDateFormatter alloc] init];
        barTelugu.dateFormat = wayWidthYet.overrideConjugateIndexAmpereBigNumeratorNot;
        NSString *backupLine = [barTelugu stringFromDate:self.selectedDate];

        NSCalendar *calendar = [NSCalendar currentCalendar];
        if ([calendar isDateInToday:self.selectedDate]) {
            self.title = wayWidthYet.fractionArtistDeliverSumActionsTornado;
        } else if ([calendar isDateInYesterday:self.selectedDate]) {
            self.title = wayWidthYet.legalPatchPerformerOddShortcutRealmRows;
        } else {
            self.title = backupLine;
        }
    } else {
        self.title = wayWidthYet.establishExceptionPluralSymbolsWonBrief;
    }
}

- (void)listOddRedAction {
    if (!self.helloShareCyclingListPhonetic) {
        return;
    }

    NSArray<NSDate *> *areBurnMixFair = [self.helloShareCyclingListPhonetic fourTypeAdd];
    if (areBurnMixFair.count == 0) {
        UIAlertController *birth = [UIAlertController alertControllerWithTitle:wayWidthYet.anyRotorHitDragReaderSeek
                                                                       message:wayWidthYet.sonHintInstantPageCreatorMen
                                                                preferredStyle:UIAlertControllerStyleAlert];
        [birth addAction:[UIAlertAction actionWithTitle:wayWidthYet.irishSenderAlienAccordingSinPortion style:UIAlertActionStyleDefault handler:nil]];
        [self presentViewController:birth animated:YES completion:nil];
        return;
    }

    UIAlertController *actionSheet = [UIAlertController alertControllerWithTitle:wayWidthYet.cloudyUnknownParserEditSlantPingBefore
                                                                         message:nil
                                                                  preferredStyle:UIAlertControllerStyleActionSheet];

    [actionSheet addAction:[UIAlertAction actionWithTitle:wayWidthYet.establishExceptionPluralSymbolsWonBrief
                                                    style:UIAlertActionStyleDefault
                                                  handler:^(UIAlertAction *action) {
        self.selectedDate = nil;
        [self gateways];
    }]];

    NSDateFormatter *barTelugu = [[NSDateFormatter alloc] init];
    barTelugu.dateFormat = wayWidthYet.overrideConjugateIndexAmpereBigNumeratorNot;

    NSCalendar *calendar = [NSCalendar currentCalendar];

    for (NSDate *date in areBurnMixFair) {
        NSString *title;
        if ([calendar isDateInToday:date]) {
            title = wayWidthYet.fractionArtistDeliverSumActionsTornado;
        } else if ([calendar isDateInYesterday:date]) {
            title = wayWidthYet.legalPatchPerformerOddShortcutRealmRows;
        } else {
            title = [barTelugu stringFromDate:date];
        }

        [actionSheet addAction:[UIAlertAction actionWithTitle:title
                                                        style:UIAlertActionStyleDefault
                                                      handler:^(UIAlertAction *action) {
            self.selectedDate = date;
            [self gateways];
        }]];
    }

    [actionSheet addAction:[UIAlertAction actionWithTitle:wayWidthYet.iconSessionsVariableCancelBitsGenerates style:UIAlertActionStyleCancel handler:nil]];

    if ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad) {
        actionSheet.popoverPresentationController.barButtonItem = self.navigationItem.rightBarButtonItems.lastObject;
    }

    [self presentViewController:actionSheet animated:YES completion:nil];
}

- (void)briefAction {
    if (!self.helloShareCyclingListPhonetic) {
        return;
    }

    NSArray *cacheMay = [self.helloShareCyclingListPhonetic checkedBeat];
    if (cacheMay.count == 0) {
        UIAlertController *birth = [UIAlertController alertControllerWithTitle:wayWidthYet.anyRotorHitDragReaderSeek
                                                                       message:wayWidthYet.catalogTelephoneDateTwentySmartDarwin
                                                                preferredStyle:UIAlertControllerStyleAlert];
        [birth addAction:[UIAlertAction actionWithTitle:wayWidthYet.irishSenderAlienAccordingSinPortion style:UIAlertActionStyleDefault handler:nil]];
        [self presentViewController:birth animated:YES completion:nil];
        return;
    }

    UIAlertController *actionSheet = [UIAlertController alertControllerWithTitle:wayWidthYet.scrollingPurplePeerHasPersistTurnRectum
                                                                         message:nil
                                                                  preferredStyle:UIAlertControllerStyleActionSheet];

    [actionSheet addAction:[UIAlertAction actionWithTitle:wayWidthYet.pieceGermanTempAnimateScannerScannerArrival
                                                    style:UIAlertActionStyleDefault
                                                  handler:^(UIAlertAction *action) {
        [self tabRowEffect];
    }]];

    for (NSURL *fileURL in cacheMay) {
        NSString *fileName = fileURL.lastPathComponent;
        [actionSheet addAction:[UIAlertAction actionWithTitle:[NSString stringWithFormat:wayWidthYet.bestRenewCursorsSubmitMixTightCandidate, fileName]
                                                        style:UIAlertActionStyleDefault
                                                      handler:^(UIAlertAction *action) {
            [self awakeWayFile:fileURL];
        }]];
    }

    [actionSheet addAction:[UIAlertAction actionWithTitle:wayWidthYet.iconSessionsVariableCancelBitsGenerates style:UIAlertActionStyleCancel handler:nil]];

    if ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad) {
        actionSheet.popoverPresentationController.barButtonItem = self.navigationItem.rightBarButtonItems.lastObject;
    }

    [self presentViewController:actionSheet animated:YES completion:nil];
}

- (void)tabRowEffect {
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        
        NSString *softWon = [self.helloShareCyclingListPhonetic talkFoldSobEra];

        dispatch_async(dispatch_get_main_queue(), ^{
            if (softWon.length > 0) {
                UIActivityViewController *acquireDog = [[UIActivityViewController alloc]
                                                       initWithActivityItems:@[softWon]
                                                       applicationActivities:nil];

                if ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad) {
                    acquireDog.popoverPresentationController.barButtonItem = self.navigationItem.rightBarButtonItems.lastObject;
                }

                [self presentViewController:acquireDog animated:YES completion:nil];
            }
        });
    });
}

- (void)awakeWayFile:(NSURL *)fileURL {
    UIActivityViewController *acquireDog = [[UIActivityViewController alloc]
                                           initWithActivityItems:@[fileURL]
                                           applicationActivities:nil];

    if ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad) {
        acquireDog.popoverPresentationController.barButtonItem = self.navigationItem.rightBarButtonItems.lastObject;
    }

    [self presentViewController:acquireDog animated:YES completion:nil];
}

@end
