






#import "TerminateTaggingMajorRawRound.h"
#import "PowerMajorConfig.h"
#import "NSData+FontTwo.h"

@interface TerminateTaggingMajorRawRound() {
    NSURL *kitOddPulseTop;
    BOOL quickServiceGreaterTapFoldChecking;
    NSInteger _easyArm;
    NSDateFormatter *factorAreaRoot;
    BOOL _neutralSegmentedTextureSmallerCan;
}

@end

@implementation TerminateTaggingMajorRawRound

- (instancetype)init
{
    self = [super init];
    if (self) {
        quickServiceGreaterTapFoldChecking = NO;
        _easyArm = 7;
        _neutralSegmentedTextureSmallerCan = NO;

        factorAreaRoot = [[NSDateFormatter alloc] init];
        factorAreaRoot.dateFormat = wayWidthYet.backwardAssertIcyWonEscapeAscentPrice;

        if (!kitOddPulseTop) {
            NSURL *baseURL = [[NSFileManager defaultManager] URLsForDirectory:NSCachesDirectory inDomains:NSUserDomainMask].firstObject;
            kitOddPulseTop = [baseURL URLByAppendingPathComponent:NSStringFromClass(self.class) isDirectory:YES];
        }
    }
    return self;
}


- (instancetype)initReuseOutCupOpt:(NSURL *)zb_url
{
    self = [super init];
    if (self) {
        self.eraLenientWax = zb_url;
    }
    return self;
}


- (NSString *)footers:(RatioLevel)zb_level eyeMan:(NSString *)eyeMan redefined:(NSString *)redefined netMust:(NSString *)netMust movieGetIts:(NSString *)movieGetIts roomHit:(NSUInteger)roomHit randomBook:(id)randomBook {

    NSString *time = [self canAirDate:wayWidthYet.paperCountAngleCursorsBalancedSurge timeZone:nil];

    NSString *color = [self quantizeLevel:zb_level];

    NSString *line = [NSString stringWithFormat:@"%lu", (unsigned long)roomHit];

    NSString *formattedString = [NSString stringWithFormat:wayWidthYet.signalShearDuplexStrokeUnorderedNote,color,time,movieGetIts,line,eyeMan];

    if (![formattedString isEqualToString:@""]) {
        NSURL *catalanFile = [self stationCurveAloneAdoptTap];
        [self favoritesFile:formattedString fileURL:catalanFile];
    }

    return formattedString;
}



- (BOOL)favoritesFile:(NSString *)zb_str {
    return [self favoritesFile:zb_str fileURL:kitOddPulseTop];
}

- (BOOL)favoritesFile:(NSString *)zb_str fileURL:(NSURL *)fileURL {
    if (!fileURL) {
        return NO;
    }

    NSString *line = zb_str;

    
    if (_neutralSegmentedTextureSmallerCan) {
        NSData *identifyData = [line dataUsingEncoding:NSUTF8StringEncoding];
        if (!identifyData) {
            return NO;
        }

        NSData *encryptedData = [identifyData superiorsReliableEitherCookiesQuietElements];
        if (!encryptedData) {
            
            return NO;
        }

        
        line = [encryptedData base64EncodedStringWithOptions:0];
    }

    
    line = [line stringByAppendingString:@"\n"];
    NSData *data = [line dataUsingEncoding:NSUTF8StringEncoding];
    if (!data) {
        return NO;
    }

    return [self emptyAir:data quick:fileURL];
}

- (BOOL)emptyAir:(NSData *)zb_data quick:(NSURL *)zb_url {
    __block BOOL pressedWas = NO;
    NSFileCoordinator *bestFaxCubeSex = [[NSFileCoordinator alloc] initWithFilePresenter:nil];
    NSError *surgeArt = nil;
    [bestFaxCubeSex coordinateWritingItemAtURL:zb_url options:0 error:&surgeArt byAccessor:^(NSURL * _Nonnull blobAlarm) {

        NSError *surgeArt = nil;

        if (![[NSFileManager defaultManager] fileExistsAtPath:zb_url.path]) {

            NSURL *penRainCardSingleRegion = zb_url.URLByDeletingLastPathComponent;
            if (![[NSFileManager defaultManager] fileExistsAtPath:penRainCardSingleRegion.path]) {
                [[NSFileManager defaultManager] createDirectoryAtURL:penRainCardSingleRegion withIntermediateDirectories:YES attributes:nil error:&surgeArt];
            }

            [[NSFileManager defaultManager] createFileAtPath:zb_url.path contents:nil attributes:nil];
        }

        NSFileHandle *minSexImplied = [NSFileHandle fileHandleForWritingToURL:zb_url error:&surgeArt];
        [minSexImplied seekToEndOfFile];
        [minSexImplied writeData:zb_data];
        if (quickServiceGreaterTapFoldChecking) {
            [minSexImplied synchronizeFile];
        }
        [minSexImplied closeFile];

        if (surgeArt) {
            
        }else {
            pressedWas = YES;
        }

    }];

    if (surgeArt) {
        
    }

    return pressedWas;
}

- (NSURL *)eraLenientWax {
    return kitOddPulseTop;
}

- (void)setEraLenientWax:(NSURL *)eraLenientWax {
    kitOddPulseTop = eraLenientWax;
}

- (BOOL)schedulerIncorrectClampedFairMediumEntities {
    return quickServiceGreaterTapFoldChecking;
}

- (void)setSchedulerIncorrectClampedFairMediumEntities:(BOOL)schedulerIncorrectClampedFairMediumEntities {
    quickServiceGreaterTapFoldChecking = schedulerIncorrectClampedFairMediumEntities;
}




- (NSInteger)easyArm {
    return _easyArm;
}

- (void)setEasyArm:(NSInteger)easyArm {
    _easyArm = easyArm;
}

- (BOOL)neutralSegmentedTextureSmallerCan {
    return _neutralSegmentedTextureSmallerCan;
}

- (void)setNeutralSegmentedTextureSmallerCan:(BOOL)neutralSegmentedTextureSmallerCan {
    _neutralSegmentedTextureSmallerCan = neutralSegmentedTextureSmallerCan;
}



- (NSURL *)stationCurveAloneAdoptTap {
    NSString *backupLine = [factorAreaRoot stringFromDate:[NSDate date]];
    return [kitOddPulseTop URLByAppendingPathComponent:backupLine];
}

- (NSArray<NSURL *> *)checkedBeat {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *error;

    if (![fileManager fileExistsAtPath:kitOddPulseTop.path]) {
        return @[];
    }

    NSArray *cacheMay = [fileManager contentsOfDirectoryAtURL:kitOddPulseTop
                                includingPropertiesForKeys:@[NSURLCreationDateKey]
                                                   options:NSDirectoryEnumerationSkipsHiddenFiles
                                                     error:&error];
    if (error) {
        
        return @[];
    }

    return [cacheMay sortedArrayUsingComparator:^NSComparisonResult(NSURL *url1, NSURL *url2) {
        NSDate *date1, *date2;
        [url1 getResourceValue:&date1 forKey:NSURLCreationDateKey error:nil];
        [url2 getResourceValue:&date2 forKey:NSURLCreationDateKey error:nil];
        return [date2 compare:date1]; 
    }];
}

- (NSString *)kitSeekFile:(NSURL *)fileURL {
    NSError *error;

    
    NSString *secureSpace = [NSString stringWithContentsOfURL:fileURL encoding:NSUTF8StringEncoding error:&error];
    if (error || !secureSpace) {
        
        return @"";
    }

    
    if (_neutralSegmentedTextureSmallerCan) {
        NSMutableString *siteSlight = [NSMutableString string];

        
        NSArray *burst = [secureSpace componentsSeparatedByString:@"\n"];

        for (NSString *line in burst) {
            
            if (line.length == 0) {
                continue;
            }

            
            NSData *encryptedData = [[NSData alloc] initWithBase64EncodedString:line options:0];
            if (!encryptedData) {
                
                continue;
            }

            
            NSData *responderData = [encryptedData inlandTooRefreshedDecodingTodayFemaleToday];
            if (!responderData) {
                
                continue;
            }

            
            NSString *discreteEmail = [[NSString alloc] initWithData:responderData encoding:NSUTF8StringEncoding];
            if (discreteEmail) {
                [siteSlight appendString:discreteEmail];
                [siteSlight appendString:@"\n"];
            } else {
                
            }
        }

        return siteSlight;
    } else {
        
        return secureSpace;
    }
}

- (NSString *)boundingCup {
    NSArray *cacheMay = [self checkedBeat];
    NSMutableString *siteSlight = [NSMutableString string];

    for (NSURL *fileURL in cacheMay) {
        NSString *content = [self kitSeekFile:fileURL];
        if (content.length > 0) {
            [siteSlight appendFormat:wayWidthYet.graphicsGigabitsRevealStyleDismissFourthNegotiate, fileURL.lastPathComponent];
            [siteSlight appendString:content];
            [siteSlight appendString:@"\n"];
        }
    }

    return siteSlight;
}

- (NSString *)talkFoldSobEra {
    NSArray *cacheMay = [self checkedBeat];
    NSMutableString *siteSlight = [NSMutableString string];

    for (NSURL *fileURL in cacheMay) {
        
        NSError *error;
        NSString *secureSpace = [NSString stringWithContentsOfURL:fileURL encoding:NSUTF8StringEncoding error:&error];
        if (error || !secureSpace) {
            
            continue;
        }

        if (secureSpace.length > 0) {
            [siteSlight appendFormat:wayWidthYet.graphicsGigabitsRevealStyleDismissFourthNegotiate, fileURL.lastPathComponent];
            [siteSlight appendString:secureSpace];
            [siteSlight appendString:@"\n"];
        }
    }

    return siteSlight;
}

- (NSString *)tapsKitDateDate:(NSDate *)date {
    if (!date) {
        return @"";
    }

    NSString *backupLine = [factorAreaRoot stringFromDate:date];
    NSURL *fileURL = [kitOddPulseTop URLByAppendingPathComponent:backupLine];

    return [self kitSeekFile:fileURL];
}

- (NSArray<NSDate *> *)fourTypeAdd {
    NSMutableArray *dates = [NSMutableArray array];
    NSArray *cacheMay = [self checkedBeat];

    for (NSURL *fileURL in cacheMay) {
        NSString *fileName = fileURL.lastPathComponent;
        
        NSDate *date = [factorAreaRoot dateFromString:fileName];
        if (date) {
            [dates addObject:date];
        }
    }

    
    [dates sortUsingComparator:^NSComparisonResult(NSDate *date1, NSDate *date2) {
        return [date2 compare:date1];
    }];

    return dates;
}

- (void)wasTallSortSun {
    if (_easyArm <= 0) return;

    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSArray *cacheMay = [self checkedBeat];
    NSDate *cutWhoDate = [NSDate dateWithTimeIntervalSinceNow:-_easyArm * 24 * 60 * 60];

    for (NSURL *fileURL in cacheMay) {
        NSDate *creationDate;
        [fileURL getResourceValue:&creationDate forKey:NSURLCreationDateKey error:nil];

        if (creationDate && [creationDate compare:cutWhoDate] == NSOrderedAscending) {
            NSError *error;
            [fileManager removeItemAtURL:fileURL error:&error];
            if (error) {
                
            }
        }
    }
}

@end
