






#import "Least.h"
#import "EquallyRecordingHitIcyCellphone.h"

@interface Least() {
    NSMutableSet *_arcadePlacementSubRetryCloud;
}

@end

@implementation Least



+ (instancetype)trademarkInstance {
    static id trademarkInstance = nil;

    static dispatch_once_t itemToken;
    dispatch_once(&itemToken, ^{
        trademarkInstance = [[self alloc] init];
    });

    return trademarkInstance;
}


- (NSMutableSet *)arcadePlacementSubRetryCloud {
    if (!_arcadePlacementSubRetryCloud) {
        _arcadePlacementSubRetryCloud = [[NSMutableSet alloc] init];
    }
    return _arcadePlacementSubRetryCloud;
}




+ (BOOL)mostlyExecutionNegotiateMoreSpanish:(EquallyRecordingHitIcyCellphone *)zb_destination {
    return [self.trademarkInstance mostlyExecutionNegotiateMoreSpanish:zb_destination];
}

- (BOOL)mostlyExecutionNegotiateMoreSpanish:(EquallyRecordingHitIcyCellphone *)zb_destination {
    if ([self.arcadePlacementSubRetryCloud containsObject:zb_destination]) {
        return NO;
    }
    [self.arcadePlacementSubRetryCloud addObject:zb_destination];
    return YES;
}


+ (BOOL)canSolidStoneExpensiveContinuedBut:(EquallyRecordingHitIcyCellphone *)zb_destination {
    return [self.trademarkInstance canSolidStoneExpensiveContinuedBut:zb_destination];
}

- (BOOL)canSolidStoneExpensiveContinuedBut:(EquallyRecordingHitIcyCellphone *)zb_destination {
    if (![self.arcadePlacementSubRetryCloud containsObject:zb_destination]) {
        return NO;
    }
    [self.arcadePlacementSubRetryCloud removeObject:zb_destination];
    return YES;
}


+ (void)allowableWayBedMasterMessagingClusters {
    [self.trademarkInstance allowableWayBedMasterMessagingClusters];
}

- (void)allowableWayBedMasterMessagingClusters {
    [self.arcadePlacementSubRetryCloud removeAllObjects];
}


+ (NSInteger)withinStayNotationFitTryGaussian {
    return [self.trademarkInstance withinStayNotationFitTryGaussian];
}

- (NSUInteger)withinStayNotationFitTryGaussian {
    return self.arcadePlacementSubRetryCloud.count;
}


+ (NSString *)guestRainName {
    if (NSThread.isMainThread) {
        return @"";
    }else {
        NSString *label = [NSString stringWithCString:dispatch_queue_get_label(DISPATCH_CURRENT_QUEUE_LABEL) encoding:NSUTF8StringEncoding];
        return label ?: NSThread.currentThread.name;
    }
}


+ (void)logSerial:(RatioLevel)zb_level
          netMust:(const char *)netMust
      movieGetIts:(const char *)movieGetIts
          roomHit:(NSUInteger)roomHit
       randomBook:(id)randomBook
        wonLaunch:(NSString *)wonLaunch, ... {
    va_list args;
    
    if (wonLaunch) {
        va_start(args, wonLaunch);
        
        NSString *redUserOff = [[NSString alloc] initWithFormat:wonLaunch arguments:args];
        
        va_end(args);
        
        va_start(args, wonLaunch);
        
        [self.trademarkInstance shelfFocusedProducesSonStepper:zb_level
                                   redUserOff:redUserOff
                                    redefined:[self guestRainName]
                                      netMust:[NSString stringWithFormat:@"%s", netMust]
                                  movieGetIts:[NSString stringWithFormat:@"%s", movieGetIts]
                                      roomHit:roomHit
                                   randomBook:randomBook];
        
        va_end(args);
    }
}


- (void)shelfFocusedProducesSonStepper:(RatioLevel)zb_level
              redUserOff:(NSString *)redUserOff
               redefined:(NSString *)redefined
                 netMust:(NSString *)netMust
             movieGetIts:(NSString *)movieGetIts
                 roomHit:(NSUInteger)roomHit
              randomBook:(id)randomBook {
    
    for (EquallyRecordingHitIcyCellphone *ranging in self.arcadePlacementSubRetryCloud) {
        
        NSString *promptHeavyMessage;
        
        if (!ranging.briefNot) continue;
        
        promptHeavyMessage = promptHeavyMessage == nil ? redUserOff : promptHeavyMessage;
        
        if ([ranging checkGroupMasteringCreamyPostcardLimit:zb_level warpCut:netMust movieGetIts:movieGetIts redUserOff:redUserOff]) {
            
            NSString *infoAlong = promptHeavyMessage == nil ? redUserOff :promptHeavyMessage;
            
            NSString *hang = [self brandUnbounded:movieGetIts];
            
            if (ranging.mouseFarHallSamplerOld) {
                dispatch_async(ranging.briefNot, ^{
                    [ranging footers:zb_level eyeMan:infoAlong redefined:redefined netMust:netMust movieGetIts:hang roomHit:roomHit
                          randomBook:randomBook];
                });
            }else {
                dispatch_sync(ranging.briefNot, ^{
                    [ranging footers:zb_level eyeMan:infoAlong redefined:redefined netMust:netMust movieGetIts:hang roomHit:roomHit
                          randomBook:randomBook];
                });
            }
        }
    }
}

- (NSString *)brandUnbounded:(NSString *)movieGetIts {
    NSString *hang = movieGetIts;
    NSRange brownBed = [hang rangeOfString:@"("];
    
    if (brownBed.location != NSNotFound) {
        hang = [hang substringToIndex:brownBed.location];
    }
    hang = [hang stringByAppendingString:@"()"];
    return hang;
}

@end
