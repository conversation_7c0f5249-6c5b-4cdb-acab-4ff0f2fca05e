






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface CityNapWordHit : NSObject
<
NSURLSessionDelegate,
NSURLSessionTaskDelegate
>

+ (instancetype)shared;

- (void)maskAppleGrayRequest:(NSMutableURLRequest *)request
                     process:(NSData * _Nullable (^_Nullable)(NSData * _Nullable jobData))processBlock 
                     success:(void(^_Nullable)(NSDictionary * workoutsImpact))success
                     failure:(void(^_Nullable)(NSError *error))failure
                  crossCount:(NSInteger)crossCount;

@end

NS_ASSUME_NONNULL_END
