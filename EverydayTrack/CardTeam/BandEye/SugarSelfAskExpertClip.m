





#import "SugarSelfAskExpertClip.h"
@import Network;

static NSString *grammarResults = nil;
static nw_path_monitor_t napMovieDaySub = NULL;

@implementation SugarSelfAskExpertClip

+ (BOOL)moodPauseConvergedLastNumerator {
    return grammarResults != nil;
}

+ (NSString *)splatOwnEasyType {
    return grammarResults ?: @"none";
}

+ (void)hexIndicesClipButterflyAmbienceTwo:(void (^)(BOOL moodPauseConvergedLastNumerator))completion {
    
    if (napMovieDaySub != NULL) {
        nw_path_monitor_cancel(napMovieDaySub);
        napMovieDaySub = NULL;
    }
    
    
    napMovieDaySub = nw_path_monitor_create();
    nw_path_monitor_set_queue(napMovieDaySub, dispatch_get_main_queue());
    
    __block nw_path_monitor_t purplePopSub = napMovieDaySub;
    nw_path_monitor_set_update_handler(napMovieDaySub, ^(nw_path_t path) {
        nw_path_status_t status = nw_path_get_status(path);
        if (status == nw_path_status_satisfied) {
            if (nw_path_uses_interface_type(path, nw_interface_type_wifi)) {
                grammarResults = @"wifi";
            } else if (nw_path_uses_interface_type(path, nw_interface_type_cellular)) {
                grammarResults = @"cellular";
            } else {
                
                grammarResults = @"other";
            }
            
            
            if (purplePopSub) {
                nw_path_monitor_cancel(purplePopSub);
                purplePopSub = NULL;
                napMovieDaySub = NULL;
            }
            
        } else {
            grammarResults = nil;
        }
        
        
        if (completion) {
            completion([self moodPauseConvergedLastNumerator]);
        }
        
    });
    
    
    nw_path_monitor_start(napMovieDaySub);
}

@end
