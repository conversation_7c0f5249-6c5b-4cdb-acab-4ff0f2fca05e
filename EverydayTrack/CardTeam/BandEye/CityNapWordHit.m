






#import "CityNapWordHit.h"

#define loadMan(obj) __weak typeof(obj) weak##obj = obj;
#define greenRate(obj) __strong typeof(obj) obj = weak##obj;

@interface CityNapWordHit()

@property (nonatomic,strong) NSURLSession *optionAllSex;

@end

@implementation CityNapWordHit


+ (instancetype)shared {
    static CityNapWordHit *shared = nil;
    static dispatch_once_t itemToken;
    dispatch_once(&itemToken, ^{
        shared = [[super allocWithZone:NULL] init];
        shared.optionAllSex = [NSURLSession sessionWithConfiguration:[NSURLSessionConfiguration defaultSessionConfiguration] delegate:shared delegateQueue:[[NSOperationQueue alloc] init]];
        shared.optionAllSex.delegateQueue.maxConcurrentOperationCount = 1;
    });
    return shared;
}

- (void)maskAppleGrayRequest:(NSMutableURLRequest *)request
                     process:(NSData * _Nullable (^_Nullable)(NSData * _Nullable jobData))processBlock
                     success:(void(^)(NSDictionary * workoutsImpact))success
                     failure:(void(^)(NSError *error))failure
                  crossCount:(NSInteger)crossCount {

    [self bezelDeepRequest:request
                   process:processBlock
                   success:success
                   failure:failure
                crossCount:crossCount
            eyeSobTheRebus:0];
}


- (void)bezelDeepRequest:(NSMutableURLRequest *)request
                 process:(NSData * _Nullable (^_Nullable)(NSData * _Nullable jobData))processBlock
                 success:(void(^)(NSDictionary * workoutsImpact))success
                 failure:(void(^)(NSError *error))failure
              crossCount:(NSInteger)crossCount
          eyeSobTheRebus:(NSInteger)eyeSobTheRebus {

    loadMan(self);
    NSURLSessionDataTask *task = [self.optionAllSex dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        greenRate(self);
        
        NSError *sameSlight = [self handleError:error response:response data:data];
        if (sameSlight) {
            

            
            if (eyeSobTheRebus < crossCount) {
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [self bezelDeepRequest:request process:processBlock success:success failure:failure crossCount:crossCount eyeSobTheRebus:eyeSobTheRebus + 1];
                });
                return;
            }

            
            if (failure) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    failure(sameSlight);
                });
            }
            return;
        }

        
        NSData *leakyTallData = processBlock ? processBlock(data) : data;
        if (!leakyTallData) {
            NSError *clickedExecutionLinerEndsDietary = [NSError errorWithDomain:@"NetworkCore"
                                                           code:-30002
                                                       userInfo:@{NSLocalizedDescriptionKey : @"Data processing failed"}];
            if (failure) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    failure(clickedExecutionLinerEndsDietary);
                });
            }
            return;
        }

        NSError *activeHis;
        NSDictionary *bookResponse = [NSJSONSerialization JSONObjectWithData:leakyTallData options:0 error:&activeHis];

        if (!activeHis && [bookResponse isKindOfClass:[NSDictionary class]]) {
            if (success) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    success(bookResponse);
                });
            }
        } else {
            
            if (failure) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    failure(activeHis);
                });
            }
        }
    }];

    [task resume];
}


- (NSError *)handleError:(NSError *)error response:(NSURLResponse *)response data:(NSData *)data {
    if (error) {
        return error;
    }

    if (!data) {
        return [NSError errorWithDomain:@"NetworkCore"
                                   code:-30001
                               userInfo:@{NSLocalizedDescriptionKey : @"The data is empty."}];
    }

    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
    if (![httpResponse isKindOfClass:[NSHTTPURLResponse class]] || httpResponse.statusCode != 200) {
        return [NSError errorWithDomain:@"NetworkCore"
                                   code:httpResponse.statusCode
                               userInfo:@{NSLocalizedDescriptionKey : [NSString stringWithFormat:@"HTTPError，code: %ld", (long)httpResponse.statusCode]}];
    }

    return nil;
}

@end
