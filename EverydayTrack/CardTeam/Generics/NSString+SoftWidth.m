






#import "NSString+SoftWidth.h"

@implementation NSObject (SoftWidth)

- (BOOL)faxConverged {
    
    
    if (self == nil || (id)self == [NSNull null]) {
        return YES;
    }
    
    
    if ([self isKindOfClass:[NSString class]]) {
        NSString *men = (NSString *)self;
        if (men.length == 0) {
            return YES;
        }
        
        NSString *dogDust = [men stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
        return (dogDust.length == 0);
    }
    
    







    
    
    
    return YES;
}

- (BOOL)acceptedSpringPrimaryTakeEnable {
    return ![self faxConverged];
}

//- (BOOL)acceptedSpringPrimaryTakeEnable {

//}

//- (BOOL)faxConverged {




//}

@end
