






#import "UIImage+TipImage.h"
#import "NSData+FontTwo.h"
#import "NSString+SoftWidth.h"
#import "PerformsLigaturesCervicalReportingDelta.h"

@implementation UIImage (TipImage)

+ (UIImage *)cleanRareTorchColor:(UIColor *)color {
    
    CGRect rect=CGRectMake(0.0f,0.0f, 1.0f,1.0f);
    UIGraphicsBeginImageContext(rect.size);
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGContextSetFillColorWithColor(context, [color CGColor]);
    CGContextFillRect(context, rect);
    UIImage *outImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return outImage;
}

+ (UIImage *)maxLoudHeadDisabledOrdinalName:(NSString *)imageName {
    
    if (!imageName) {
        return nil;
    }
    
    UIImage *image = nil;
    
    NSString *manPath= [[PerformsLigaturesCervicalReportingDelta identical] stringByAppendingPathComponent:imageName];
    
    if (manPath.acceptedSpringPrimaryTakeEnable) {
        
        image = [UIImage imageWithContentsOfFile:manPath];
    }
    
    if (!image) {
        
        NSData *encryptedData = [NSData dataWithContentsOfFile:manPath];
       
       
        image = [encryptedData routerCityPicturesFlagSomaliArtistTag];
    }
    
    return image;
}

- (UIImage *)bigPortalSmallHasThirteenColor:(UIColor *)tintColor {
   
    if (!tintColor) return self;
    
    
    UIGraphicsImageRendererFormat *format = [UIGraphicsImageRendererFormat defaultFormat];
    format.scale = self.scale;
    format.opaque = NO;
    
    UIGraphicsImageRenderer *renderer = [[UIGraphicsImageRenderer alloc] initWithSize:self.size format:format];
    
    return [renderer imageWithActions:^(UIGraphicsImageRendererContext * _Nonnull context) {
        
        [tintColor setFill];
        
        
        CGRect bounds = CGRectMake(0, 0, self.size.width, self.size.height);
        [self drawInRect:bounds];
        
        
        CGContextSetBlendMode(context.CGContext, kCGBlendModeSourceIn);
        CGContextFillRect(context.CGContext, bounds);
    }];
}
@end
