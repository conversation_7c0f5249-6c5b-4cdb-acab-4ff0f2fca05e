






#import "UIDevice+TryDevice.h"
#import "TerahertzManager.h"
@import UIKit;

@implementation UIDevice (TryDevice)

static NSInteger andPub = -1;
+ (BOOL)andPub {
    if (andPub < 0) {
        andPub = [UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad ? 1 : 0;
    }
    return andPub > 0;
}

+ (BOOL)loveBars {
    if (@available(iOS 11.0, *)) {
        
        UIWindow *window = TerahertzManager.shared.useLessMeterWindow;
        
        UIEdgeInsets safeArea = window.safeAreaInsets;
        
        
        BOOL kitVoice = ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPhone);
        
        
        return kitVoice && (
            safeArea.top > 20.0 ||          
            safeArea.left > 0 ||            
            safeArea.right > 0              
        );
    }
    return NO; 
}

@end
