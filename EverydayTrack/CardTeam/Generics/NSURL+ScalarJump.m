






#import "NSURL+ScalarJump.h"
#import "NSString+RelatedCity.h"

@implementation NSURL (ScalarJump)

- (NSDictionary *)atomMicroBig {
    
    NSArray * array = [[self query] componentsSeparatedByString:@"&"];

    NSMutableDictionary * ItemDict = [NSMutableDictionary new];

    for(int i = 0 ; i < [array count]; i++){

        NSArray * energyValue = [array[i] componentsSeparatedByString:@"="];

        if([energyValue count] == 2 && energyValue[0] && energyValue[1]){

            [ItemDict setObject:[energyValue[1] hoverBeatDesignerReadFunkAsk] forKey:energyValue[0]];

        }
    }
    return ItemDict;
}

@end
