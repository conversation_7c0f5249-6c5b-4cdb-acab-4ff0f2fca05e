






#import "UIViewController+FatViewController.h"

@implementation UIViewController (FatViewController)

- (void)playingSubscribeGramParserGallonViewController:(UIViewController *)bin {
    if (bin) {
        [bin willMoveToParentViewController:self];
        [self addChildViewController:bin];
        [self.view addSubview:bin.view];
        
        
        bin.view.translatesAutoresizingMaskIntoConstraints = NO;

        UIView *superview = bin.view.superview;
        [NSLayoutConstraint activateConstraints:@[
            [bin.view.topAnchor constraintEqualToAnchor:superview.topAnchor],
            [bin.view.leadingAnchor constraintEqualToAnchor:superview.leadingAnchor],
            [bin.view.bottomAnchor constraintEqualToAnchor:superview.bottomAnchor],
            [bin.view.trailingAnchor constraintEqualToAnchor:superview.trailingAnchor]
        ]];
        
        [bin didMoveToParentViewController:self];
    }
}

- (void)sexMileSkinButSlowFrameViewController {
    if (self && self.parentViewController) {
        [self willMoveToParentViewController:nil];
        [[self view] removeFromSuperview];
        [self removeFromParentViewController];
    }
}

- (void)preferredIncorrectBoundaryBoldfaceDecideEscapeDecision {
    for (UIViewController *bin in self.childViewControllers) {
        [bin sexMileSkinButSlowFrameViewController];
    }
}

@end
